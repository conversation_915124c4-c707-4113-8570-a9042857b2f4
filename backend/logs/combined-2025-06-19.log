{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:35:08:358"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:01:391"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:09:399"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:22:3922"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:39:41:3941"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:42:08:428"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:44:58:4458"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:44:58:4458"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:11:4511"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:11:4511"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:45:21:4521"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:46:49:4649"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:46:49:4649"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:46:54:4654"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:46:54:4654"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:47:00:470"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:47:05:475"}
{"level":"warn","message":"⚠️ Redis connection failed - running in degraded mode","timestamp":"2025-06-19 15:50:10:5010"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 15:50:15:5015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:37:40:3740"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:40:21:4021"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:40:41:4041"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"warn","message":"⚠️ Redis connection test timeout - continuing without Redis","timestamp":"2025-06-19 16:43:08:438"}
