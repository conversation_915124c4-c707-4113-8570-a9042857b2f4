{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:37:32:3732"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:15:4015"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:40:35:4035"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:43:03:433"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:28:4528"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:28:4528"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:41:4541"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:41:4541"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:52:4552"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:45:52:4552"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:46:13:4613"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:46:13:4613"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:09:489"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:48:30:4830"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:55:4955"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:56:4956"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:49:56:4956"}
{"error":{"errno":-5,"code":"EIO","syscall":"write"},"level":"error","message":"Uncaught exception detected, shutting down...","timestamp":"2025-06-19 16:50:13:5013"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:609:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:499:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 16:50:34:5034"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:15:37:1537"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:15:37:1537"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:46:1646"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:46:1646"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:56:1656"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:16:56:1656"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:490:33\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:18:54:1854"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:20:32:2032"}
{"level":"error","message":"Redis PING failed: Stream isn't writeable and enableOfflineQueue options is false","stack":"Error: Stream isn't writeable and enableOfflineQueue options is false\n    at EventEmitter.sendCommand (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/Redis.js:362:32)\n    at EventEmitter.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at OptimizedRedisWrapper.<anonymous> (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:230:31)\n    at Generator.next (<anonymous>)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:8:71\n    at new Promise (<anonymous>)\n    at __awaiter (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:4:12)\n    at OptimizedRedisWrapper.ping (/media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/services/redis/OptimizedRedisWrapper.ts:217:16)\n    at /media/gm-hridoy/study/programming-hero/green-uni-mind/backend/src/app/config/redis.ts:600:21\n    at Generator.next (<anonymous>)","timestamp":"2025-06-19 17:20:32:2032"}
