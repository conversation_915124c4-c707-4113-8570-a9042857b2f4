import Redis from 'ioredis';
import config from './index';
import OptimizedRedisWrapper from '../services/redis/OptimizedRedisWrapper';

// Redis connection state
let redisConnectionState = {
  isConnected: false,
  lastError: null as Error | null,
  retryCount: 0,
  maxRetries: 5
};

// Redis connection configuration with optimized settings and graceful degradation
const redisConfig = (() => {
  // Base configuration for all Redis clients
  const baseConfig = {
    connectionName: 'green-uni-mind',
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    // Connection pool settings for better performance
    family: 4, // Use IPv4
    keepAlive: 30000, // 30 seconds keep alive
    connectTimeout: 15000, // Increased timeout for cloud deployments
    commandTimeout: 10000, // Increased command timeout
    enableOfflineQueue: false, // Disable to prevent memory buildup
    retryDelayOnClusterDown: 1000,
    // Reconnection settings
    reconnectOnError: (err: Error) => {
      // Only reconnect for specific recoverable errors
      const reconnectableErrors = ['READONLY', 'LOADING', 'MASTERDOWN', 'ECONNRESET', 'ETIMEDOUT'];
      return reconnectableErrors.some(error => err.message.includes(error));
    },
    // Retry strategy with exponential backoff
    retryDelayOnFailover: (times: number) => Math.min(times * 50, 2000),
  };

  // Use individual host/port/password configuration
  const host = config.redis.host || 'localhost';
  const port = config.redis.port || 6379;
  const password = config.redis.password || '';

  return {
    ...baseConfig,
    host,
    port,
    password,
    // TLS configuration for Upstash Redis
    tls: host && host.includes('upstash.io') ? {} : undefined,
  };
})();

// Setup connection event handlers for Redis clients (MUST be declared before createRedisClient)
const setupClientEvents = (client: Redis, name: string) => {
  client.on('connect', () => {
    console.log(`✅ Redis ${name} client connected successfully`);
    if (name === 'primary') {
      redisConnectionState.isConnected = true;
      redisConnectionState.lastError = null;
      redisConnectionState.retryCount = 0;
    }
  });

  client.on('ready', () => {
    console.log(`✅ Redis ${name} client is ready to accept commands`);
  });

  client.on('error', (error) => {
    console.error(`❌ Redis ${name} client connection error:`, error);
    if (name === 'primary') {
      redisConnectionState.isConnected = false;
      redisConnectionState.lastError = error;
      redisConnectionState.retryCount++;
    }

    // Don't throw errors, just log them for graceful degradation
    if (redisConnectionState.retryCount > redisConnectionState.maxRetries) {
      console.warn(`⚠️ Redis ${name} client exceeded max retries, switching to graceful degradation mode`);
    }
  });

  client.on('close', () => {
    console.log(`⚠️ Redis ${name} client connection closed`);
    if (name === 'primary') {
      redisConnectionState.isConnected = false;
    }
  });

  client.on('reconnecting', (delay: number) => {
    console.log(`🔄 Redis ${name} client reconnecting in ${delay}ms...`);
  });

  client.on('end', () => {
    console.log(`🔚 Redis ${name} client connection ended`);
    if (name === 'primary') {
      redisConnectionState.isConnected = false;
    }
  });
};

// Create a mock Redis client for graceful degradation
const createMockRedisClient = (name: string): any => {
  console.warn(`⚠️ Creating mock Redis client for ${name} due to connection issues`);

  const mockClient = {
    status: 'disconnected',
    on: () => mockClient,
    connect: async () => { throw new Error(`Redis ${name} client is unavailable`); },
    disconnect: async () => {},
    ping: async () => { throw new Error(`Redis ${name} client is unavailable`); },
    get: async () => null,
    set: async () => 'OK',
    del: async () => 0,
    keys: async () => [],
    exists: async () => 0,
    expire: async () => 0,
    ttl: async () => -1,
    info: async () => '',
    memory: async () => 0,
    // Add other commonly used Redis methods that return safe defaults
    hget: async () => null,
    hset: async () => 0,
    hdel: async () => 0,
    hgetall: async () => ({}),
    sadd: async () => 0,
    srem: async () => 0,
    smembers: async () => [],
    zadd: async () => 0,
    zrem: async () => 0,
    zrange: async () => [],
    incr: async () => 1,
    decr: async () => 0,
  };

  return mockClient;
};

// Helper function to create Redis client with error handling
const createRedisClient = (name: string, customConfig?: any): Redis => {
  try {
    const clientConfig = { ...redisConfig, ...customConfig };

    // Always use host/port/password configuration (no REDIS_URL)
    const client = new Redis(clientConfig);

    // Set up event handlers for this client
    setupClientEvents(client, name);

    return client;
  } catch (error) {
    console.error(`❌ Failed to create Redis ${name} client:`, error);
    redisConnectionState.lastError = error as Error;
    // Return a mock client that fails gracefully
    return createMockRedisClient(name);
  }
};

// Create primary Redis client instance with optimized wrapper
const rawRedisClient = createRedisClient('primary');
const redis = new OptimizedRedisWrapper(rawRedisClient);

// Create separate Redis clients for different use cases with optimized wrappers
const rawRedisAuth = createRedisClient('auth');
const redisAuth = new OptimizedRedisWrapper(rawRedisAuth); // For authentication operations

const rawRedisCache = createRedisClient('cache');
const redisCache = new OptimizedRedisWrapper(rawRedisCache); // For caching operations

const rawRedisJobs = createRedisClient('jobs');
const redisJobs = new OptimizedRedisWrapper(rawRedisJobs); // For job queue operations

const rawRedisSessions = createRedisClient('sessions');
const redisSessions = new OptimizedRedisWrapper(rawRedisSessions); // For session management

// Import and use the new Redis Service Manager
import {
  redisServiceManager,
  redis as newRedis,
  redisAuth as newRedisAuth,
  redisCache as newRedisCache,
  redisJobs as newRedisJobs,
  redisSessions as newRedisSessions,
  redisMonitoring,
  cacheService
} from '../services/redis/RedisServiceManager';

// Export the new Redis clients and services (maintaining backward compatibility)
export {
  redis,
  redisAuth,
  redisCache,
  redisJobs,
  redisSessions,
  redisServiceManager,
  redisMonitoring,
  cacheService
};

// Also export the new clients for migration
export {
  newRedis,
  newRedisAuth,
  newRedisCache,
  newRedisJobs,
  newRedisSessions
};

// OTP-related Redis operations
export const otpOperations = {
  // Store OTP with TTL (5 minutes = 300 seconds)
  async setOTP(email: string, otp: string, ttlSeconds: number = 300): Promise<void> {
    const key = `otp:${email}`;
    await redis.setex(key, ttlSeconds, otp);
    console.log(`✅ OTP stored for ${email} with TTL ${ttlSeconds}s`);
  },

  // Get OTP for email
  async getOTP(email: string): Promise<string | null> {
    const key = `otp:${email}`;
    const otp = await redis.get(key);
    return otp;
  },

  // Delete OTP after successful verification
  async deleteOTP(email: string): Promise<void> {
    const key = `otp:${email}`;
    await redis.del(key);
    console.log(`✅ OTP deleted for ${email}`);
  },

  // Check if OTP exists and get TTL
  async getOTPTTL(email: string): Promise<number> {
    const key = `otp:${email}`;
    return await redis.ttl(key);
  },

  // Professional-grade rate limiting for OTP requests
  async checkOTPRateLimit(email: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    isLocked: boolean;
    lockReason?: string;
    lockDuration?: number;
  }> {
    const attemptsKey = `otp_attempts:${email}`;
    const lockKey = `otp_lock:${email}`;
    const maxAttempts = 3;
    const windowSeconds = 1800; // 30 minutes
    const lockDuration = 1800; // 30 minutes
    const extendedLockDuration = 3600; // 1 hour

    try {
      // Check if account is currently locked
      const lockData = await redis.get(lockKey);
      if (lockData) {
        const lock = JSON.parse(lockData);
        const ttl = await redis.ttl(lockKey);

        // If locked account receives more requests, extend lock to 1 hour
        if (lock.attempts >= maxAttempts) {
          await redis.setex(lockKey, extendedLockDuration, JSON.stringify({
            ...lock,
            attempts: lock.attempts + 1,
            extendedLock: true,
            lastAttempt: Date.now()
          }));

          console.warn(`🚨 Suspicious activity detected for ${email}: Extended lock applied`);

          return {
            allowed: false,
            remaining: 0,
            resetTime: Date.now() + (extendedLockDuration * 1000),
            isLocked: true,
            lockReason: 'Too many requests detected. Account locked for 1 hour due to suspicious activity.',
            lockDuration: extendedLockDuration
          };
        }

        return {
          allowed: false,
          remaining: 0,
          resetTime: Date.now() + (ttl * 1000),
          isLocked: true,
          lockReason: 'Account temporarily locked due to too many OTP requests. Please try again in 30 minutes.',
          lockDuration: ttl
        };
      }

      // Check current attempts
      const current = await redis.get(attemptsKey);
      const attempts = current ? parseInt(current) : 0;

      if (attempts >= maxAttempts) {
        // Lock the account
        const lockInfo = {
          attempts: attempts + 1,
          lockedAt: Date.now(),
          reason: 'rate_limit_exceeded'
        };

        await redis.setex(lockKey, lockDuration, JSON.stringify(lockInfo));
        await redis.del(attemptsKey); // Clean up attempts counter

        console.warn(`🔒 Account locked for ${email}: Rate limit exceeded (${attempts + 1} attempts)`);

        return {
          allowed: false,
          remaining: 0,
          resetTime: Date.now() + (lockDuration * 1000),
          isLocked: true,
          lockReason: 'Account temporarily locked due to too many OTP requests. Please try again in 30 minutes.',
          lockDuration: lockDuration
        };
      }

      // Increment attempts
      if (current) {
        await redis.incr(attemptsKey);
      } else {
        await redis.setex(attemptsKey, windowSeconds, '1');
      }

      const ttl = await redis.ttl(attemptsKey);
      const newAttempts = attempts + 1;

      console.log(`📊 OTP rate limit check for ${email}: ${newAttempts}/${maxAttempts} attempts`);

      return {
        allowed: true,
        remaining: maxAttempts - newAttempts,
        resetTime: Date.now() + (ttl * 1000),
        isLocked: false
      };

    } catch (error) {
      console.error('❌ Redis rate limiting error:', error);
      // Graceful degradation - allow request if Redis fails
      return {
        allowed: true,
        remaining: 2,
        resetTime: Date.now() + (windowSeconds * 1000),
        isLocked: false
      };
    }
  },

  // Resend cooldown management (1-minute cooldown between resend requests)
  async checkResendCooldown(email: string): Promise<{
    allowed: boolean;
    remainingTime: number;
  }> {
    const cooldownKey = `otp_resend_cooldown:${email}`;
    const cooldownSeconds = 60; // 1 minute

    try {
      const exists = await redis.exists(cooldownKey);

      if (exists) {
        const ttl = await redis.ttl(cooldownKey);
        console.log(`⏰ Resend cooldown active for ${email}: ${ttl} seconds remaining`);

        return {
          allowed: false,
          remainingTime: ttl
        };
      }

      // Set cooldown
      await redis.setex(cooldownKey, cooldownSeconds, '1');
      console.log(`✅ Resend cooldown set for ${email}: ${cooldownSeconds} seconds`);

      return {
        allowed: true,
        remainingTime: 0
      };

    } catch (error) {
      console.error('❌ Redis resend cooldown error:', error);
      // Graceful degradation - allow resend if Redis fails
      return {
        allowed: true,
        remainingTime: 0
      };
    }
  },

  // Get comprehensive rate limiting status
  async getRateLimitStatus(email: string): Promise<{
    attempts: number;
    remaining: number;
    isLocked: boolean;
    lockReason?: string;
    lockTimeRemaining?: number;
    resendCooldownRemaining?: number;
    canResend: boolean;
  }> {
    const attemptsKey = `otp_attempts:${email}`;
    const lockKey = `otp_lock:${email}`;
    const cooldownKey = `otp_resend_cooldown:${email}`;
    const maxAttempts = 3;

    try {
      const [attempts, lockData, cooldownTTL] = await Promise.all([
        redis.get(attemptsKey),
        redis.get(lockKey),
        redis.ttl(cooldownKey)
      ]);

      const currentAttempts = attempts ? parseInt(attempts) : 0;
      const isLocked = !!lockData;
      let lockInfo = null;
      let lockTimeRemaining = 0;

      if (isLocked) {
        lockInfo = JSON.parse(lockData);
        lockTimeRemaining = await redis.ttl(lockKey);
      }

      return {
        attempts: currentAttempts,
        remaining: Math.max(0, maxAttempts - currentAttempts),
        isLocked,
        lockReason: lockInfo?.extendedLock
          ? 'Too many requests detected. Account locked for 1 hour due to suspicious activity.'
          : isLocked
            ? 'Account temporarily locked due to too many OTP requests. Please try again in 30 minutes.'
            : undefined,
        lockTimeRemaining: lockTimeRemaining > 0 ? lockTimeRemaining : undefined,
        resendCooldownRemaining: cooldownTTL > 0 ? cooldownTTL : undefined,
        canResend: !isLocked && cooldownTTL <= 0
      };

    } catch (error) {
      console.error('❌ Redis rate limit status error:', error);
      return {
        attempts: 0,
        remaining: maxAttempts,
        isLocked: false,
        canResend: true
      };
    }
  },

  // Utility functions for testing and debugging
  async clearOTP(email: string): Promise<void> {
    const key = `otp:${email}`;
    await redis.del(key);
    console.log(`✅ OTP cleared for ${email}`);
  },

  async clearRateLimit(email: string): Promise<void> {
    const attemptsKey = `otp_attempts:${email}`;
    const lockKey = `otp_lock:${email}`;
    const cooldownKey = `otp_resend_cooldown:${email}`;

    await Promise.all([
      redis.del(attemptsKey),
      redis.del(lockKey),
      redis.del(cooldownKey)
    ]);
    console.log(`✅ Rate limit data cleared for ${email}`);
  },

  // Clear all OTP-related data for an email (useful for testing)
  async clearAllOTPData(email: string): Promise<void> {
    await Promise.all([
      this.clearOTP(email),
      this.clearRateLimit(email)
    ]);
    console.log(`✅ All OTP data cleared for ${email}`);
  },

  // Reset rate limiting for an email (admin function)
  async resetRateLimit(email: string): Promise<void> {
    await this.clearRateLimit(email);
    console.log(`🔓 Rate limit reset for ${email}`);
  }
};

// Test Redis connection (enhanced with service manager)
export const testRedisConnection = async (): Promise<boolean> => {
  try {
    // For Upstash, use a simpler connection test
    if (process.env.REDIS_HOST?.includes('upstash')) {
      console.log('🔍 Testing Upstash Redis connection...');

      // Simple ping test with timeout using optimized wrapper
      const pingPromise = redis.ping();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), 5000)
      );

      await Promise.race([pingPromise, timeoutPromise]);
      console.log('✅ Upstash Redis connection test successful');

      // Log operation stats
      const stats = redis.getOperationStats();
      console.log('📊 Redis operation stats:', stats);

      return true;
    }

    // Test legacy connection for other Redis providers
    await redis.ping();
    console.log('✅ Legacy Redis connection test successful');

    // Test new service manager
    const healthCheck = await redisServiceManager.healthCheck();
    console.log('✅ Redis Service Manager health check:', healthCheck.overall);

    // Test cache service
    await cacheService.set('test:connection', 'success', 10);
    const testValue = await cacheService.get('test:connection');
    await cacheService.del('test:connection');

    if (testValue === 'success') {
      console.log('✅ Cache service test successful');
    }

    return healthCheck.overall !== 'unhealthy';
  } catch (error) {
    console.error('❌ Redis connection test failed:', error);
    return false;
  }
};

// Import circuit breaker functionality
import { CircuitBreakerFactory, createResilientRedisOperation } from '../services/redis/CircuitBreakerService';

// Initialize circuit breakers for different Redis operations
const redisCircuitBreakers = {
  primary: CircuitBreakerFactory.getCircuitBreaker('redis-primary', {
    failureThreshold: 3,
    recoveryTimeout: 30000, // 30 seconds
    expectedErrorRate: 0.3
  }),
  auth: CircuitBreakerFactory.getCircuitBreaker('redis-auth', {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    expectedErrorRate: 0.3
  }),
  cache: CircuitBreakerFactory.getCircuitBreaker('redis-cache', {
    failureThreshold: 5,
    recoveryTimeout: 60000, // 1 minute for cache operations
    expectedErrorRate: 0.2
  }),
  sessions: CircuitBreakerFactory.getCircuitBreaker('redis-sessions', {
    failureThreshold: 4,
    recoveryTimeout: 30000,
    expectedErrorRate: 0.25
  })
};

// Utility functions for Redis operations with graceful degradation and circuit breaker protection
export const safeRedisOperation = async <T>(
  operation: () => Promise<T>,
  fallback: T,
  operationName: string = 'Redis operation',
  circuitBreakerName: keyof typeof redisCircuitBreakers = 'primary'
): Promise<T> => {
  try {
    // Check connection state first
    if (!redisConnectionState.isConnected && redisConnectionState.retryCount > redisConnectionState.maxRetries) {
      console.warn(`⚠️ ${operationName} skipped - Redis unavailable, using fallback`);
      return fallback;
    }

    // Use circuit breaker for the operation
    const circuitBreaker = redisCircuitBreakers[circuitBreakerName];
    return await circuitBreaker.execute(
      operation,
      async () => {
        console.warn(`⚠️ ${operationName} circuit breaker fallback triggered`);
        return fallback;
      }
    );
  } catch (error) {
    console.error(`❌ ${operationName} failed:`, error);
    redisConnectionState.lastError = error as Error;
    return fallback;
  }
};

// Enhanced Redis operation with circuit breaker
const resilientRedisOperation = <T>(
  operation: () => Promise<T>,
  fallback?: () => Promise<T>,
  circuitBreakerName: string = 'redis-resilient'
): (() => Promise<T>) => {
  return createResilientRedisOperation(operation, fallback, circuitBreakerName);
};

// Health check function for Redis with circuit breaker protection
export const isRedisHealthy = async (): Promise<boolean> => {
  try {
    return await safeRedisOperation(
      async () => {
        await redis.ping();
        return true;
      },
      false,
      'Redis health check',
      'primary'
    );
  } catch (error) {
    console.error('❌ Redis health check failed:', error);
    return false;
  }
};

// Get circuit breaker status for monitoring
const getCircuitBreakerStatus = () => {
  const status: Record<string, any> = {};

  Object.entries(redisCircuitBreakers).forEach(([name, breaker]) => {
    status[name] = {
      state: breaker.getState(),
      metrics: breaker.getMetrics(),
      isHealthy: breaker.isHealthy(),
      config: breaker.getConfig()
    };
  });

  return status;
};

// Force circuit breaker states for emergency situations
const emergencyCircuitBreakerControl = {
  openAll: () => {
    Object.values(redisCircuitBreakers).forEach(breaker => breaker.forceOpen());
    console.warn('🚨 All Redis circuit breakers forced OPEN');
  },

  closeAll: () => {
    Object.values(redisCircuitBreakers).forEach(breaker => breaker.forceClose());
    console.log('✅ All Redis circuit breakers forced CLOSED');
  },

  resetAll: () => {
    Object.values(redisCircuitBreakers).forEach(breaker => breaker.forceClose());
    console.log('🔄 All Redis circuit breakers reset');
  }
};

// Get Redis connection status
export const getRedisStatus = () => ({
  ...redisConnectionState,
  clientStatus: redis.status,
});

// Graceful shutdown function
export const shutdownRedis = async (): Promise<void> => {
  console.log('🔄 Shutting down Redis connections...');

  const clients = [redis, redisAuth, redisCache, redisJobs, redisSessions];
  const shutdownPromises = clients.map(async (client, index) => {
    try {
      if (client && typeof client.disconnect === 'function') {
        await client.disconnect();
        console.log(`✅ Redis client ${index + 1} disconnected`);
      }
    } catch (error) {
      console.error(`❌ Error disconnecting Redis client ${index + 1}:`, error);
    }
  });

  await Promise.allSettled(shutdownPromises);
  console.log('✅ Redis shutdown completed');
};

export {
  redisConnectionState,
  redisCircuitBreakers,
  getCircuitBreakerStatus,
  emergencyCircuitBreakerControl,
  resilientRedisOperation
};
export default redis;
