import { Logger } from '../../config/logger';

/**
 * Redis Operation Limiter - Dramatically reduces Redis operations to stay within free tier
 * 
 * Key strategies:
 * 1. In-memory caching with TTL
 * 2. Operation batching and deduplication
 * 3. Circuit breaker for failed operations
 * 4. Local operation counting and limits
 * 5. Smart fallback mechanisms
 */
export class RedisOperationLimiter {
  private static instance: RedisOperationLimiter;
  private memoryCache = new Map<string, { value: any; expires: number }>();
  private operationCount = 0;
  private operationLimit = 1000; // Max operations per hour
  private operationWindow = 60 * 60 * 1000; // 1 hour in ms
  private windowStart = Date.now();
  private pendingOperations = new Map<string, Promise<any>>();
  private batchQueue: Array<{ key: string; operation: string; resolve: Function; reject: Function }> = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private circuitBreakerOpen = false;
  private circuitBreakerTimeout = 60000; // 1 minute
  private circuitBreakerOpenTime = 0;
  private failureCount = 0;
  private failureThreshold = 5;

  private constructor() {
    // Clean up expired cache entries every 5 minutes
    setInterval(() => this.cleanupExpiredCache(), 5 * 60 * 1000);
    
    // Reset operation count every hour
    setInterval(() => this.resetOperationCount(), this.operationWindow);
  }

  static getInstance(): RedisOperationLimiter {
    if (!RedisOperationLimiter.instance) {
      RedisOperationLimiter.instance = new RedisOperationLimiter();
    }
    return RedisOperationLimiter.instance;
  }

  /**
   * Check if we can perform a Redis operation
   */
  private canPerformOperation(): boolean {
    // Reset window if needed
    if (Date.now() - this.windowStart > this.operationWindow) {
      this.resetOperationCount();
    }

    // Check circuit breaker
    if (this.circuitBreakerOpen) {
      if (Date.now() - this.circuitBreakerOpenTime > this.circuitBreakerTimeout) {
        this.circuitBreakerOpen = false;
        this.failureCount = 0;
        Logger.info('🔄 Redis circuit breaker closed - attempting operations');
      } else {
        return false;
      }
    }

    // Check operation limit
    return this.operationCount < this.operationLimit;
  }

  /**
   * Record operation and handle failures
   */
  private recordOperation(success: boolean): void {
    this.operationCount++;
    
    if (!success) {
      this.failureCount++;
      if (this.failureCount >= this.failureThreshold) {
        this.circuitBreakerOpen = true;
        this.circuitBreakerOpenTime = Date.now();
        Logger.warn(`⚠️ Redis circuit breaker opened after ${this.failureCount} failures`);
      }
    } else {
      this.failureCount = Math.max(0, this.failureCount - 1);
    }
  }

  /**
   * Get value with aggressive caching
   */
  async get(key: string, redisGetFn: () => Promise<any>, ttlSeconds = 300): Promise<any> {
    // Check memory cache first
    const cached = this.memoryCache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.value;
    }

    // Check if operation is already pending (deduplication)
    if (this.pendingOperations.has(key)) {
      return this.pendingOperations.get(key);
    }

    // Check if we can perform Redis operation
    if (!this.canPerformOperation()) {
      Logger.warn(`⚠️ Redis operation limit reached or circuit breaker open for key: ${key}`);
      return null; // Return null instead of hitting Redis
    }

    // Create pending operation
    const operation = this.performRedisGet(key, redisGetFn, ttlSeconds);
    this.pendingOperations.set(key, operation);

    try {
      const result = await operation;
      return result;
    } finally {
      this.pendingOperations.delete(key);
    }
  }

  private async performRedisGet(key: string, redisGetFn: () => Promise<any>, ttlSeconds: number): Promise<any> {
    try {
      const value = await redisGetFn();
      this.recordOperation(true);
      
      // Cache in memory
      if (value !== null) {
        this.memoryCache.set(key, {
          value,
          expires: Date.now() + (ttlSeconds * 1000)
        });
      }
      
      return value;
    } catch (error) {
      this.recordOperation(false);
      Logger.error(`Redis GET failed for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set value with batching
   */
  async set(key: string, value: any, redisSetFn: () => Promise<any>, ttlSeconds = 300): Promise<boolean> {
    // Always cache in memory first
    this.memoryCache.set(key, {
      value,
      expires: Date.now() + (ttlSeconds * 1000)
    });

    // Check if we can perform Redis operation
    if (!this.canPerformOperation()) {
      Logger.warn(`⚠️ Redis operation limit reached for SET key: ${key} - using memory cache only`);
      return true; // Return success since we cached in memory
    }

    // Add to batch queue for Redis operation
    return new Promise((resolve, reject) => {
      this.batchQueue.push({ key, operation: 'set', resolve, reject });
      this.scheduleBatchExecution(redisSetFn);
    });
  }

  /**
   * Delete value
   */
  async del(key: string, redisDelFn: () => Promise<any>): Promise<boolean> {
    // Remove from memory cache immediately
    this.memoryCache.delete(key);

    // Check if we can perform Redis operation
    if (!this.canPerformOperation()) {
      Logger.warn(`⚠️ Redis operation limit reached for DEL key: ${key}`);
      return true; // Return success since we removed from memory
    }

    try {
      await redisDelFn();
      this.recordOperation(true);
      return true;
    } catch (error) {
      this.recordOperation(false);
      Logger.error(`Redis DEL failed for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Schedule batch execution
   */
  private scheduleBatchExecution(redisSetFn: () => Promise<any>): void {
    if (this.batchTimer) {
      return; // Already scheduled
    }

    this.batchTimer = setTimeout(async () => {
      await this.executeBatch(redisSetFn);
      this.batchTimer = null;
    }, 100); // 100ms batch window
  }

  /**
   * Execute batched operations
   */
  private async executeBatch(redisSetFn: () => Promise<any>): Promise<void> {
    if (this.batchQueue.length === 0) {
      return;
    }

    const batch = [...this.batchQueue];
    this.batchQueue.length = 0;

    // Group by operation type
    const setBatch = batch.filter(op => op.operation === 'set');

    // Execute SET operations
    if (setBatch.length > 0) {
      try {
        // For now, execute one by one (can be optimized with pipeline)
        for (const op of setBatch) {
          try {
            await redisSetFn();
            this.recordOperation(true);
            op.resolve(true);
          } catch (error) {
            this.recordOperation(false);
            Logger.error(`Batch Redis SET failed for key ${op.key}:`, error);
            op.resolve(true); // Still resolve as success since we have memory cache
          }
        }
      } catch (error) {
        // Resolve all as success since we have memory cache
        setBatch.forEach(op => op.resolve(true));
      }
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expires <= now) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      Logger.info(`🧹 Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  /**
   * Reset operation count
   */
  private resetOperationCount(): void {
    this.operationCount = 0;
    this.windowStart = Date.now();
    Logger.info(`🔄 Redis operation count reset. Cache size: ${this.memoryCache.size}`);
  }

  /**
   * Get current stats
   */
  getStats(): {
    operationCount: number;
    operationLimit: number;
    cacheSize: number;
    circuitBreakerOpen: boolean;
    failureCount: number;
  } {
    return {
      operationCount: this.operationCount,
      operationLimit: this.operationLimit,
      cacheSize: this.memoryCache.size,
      circuitBreakerOpen: this.circuitBreakerOpen,
      failureCount: this.failureCount
    };
  }

  /**
   * Force clear all caches (for testing)
   */
  clearAll(): void {
    this.memoryCache.clear();
    this.pendingOperations.clear();
    this.batchQueue.length = 0;
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }
  }
}

export const redisOperationLimiter = RedisOperationLimiter.getInstance();
