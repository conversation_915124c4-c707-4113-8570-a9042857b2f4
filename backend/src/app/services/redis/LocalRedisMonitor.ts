import { Logger } from '../../config/logger';

/**
 * Local Redis Monitor - Tracks Redis operations without using Redis
 * Helps monitor and reduce Redis usage to stay within free tier limits
 */
export class LocalRedisMonitor {
  private static instance: LocalRedisMonitor;
  private operationCounts = new Map<string, number>();
  private operationHistory: Array<{ operation: string; timestamp: number; success: boolean }> = [];
  private maxHistorySize = 1000;
  private startTime = Date.now();
  private alerts: Array<{ message: string; timestamp: number; level: 'info' | 'warn' | 'error' }> = [];
  private maxAlerts = 100;

  // Thresholds for alerts
  private readonly HOURLY_OPERATION_LIMIT = 1000;
  private readonly DAILY_OPERATION_LIMIT = 10000;
  private readonly HIGH_FAILURE_RATE_THRESHOLD = 0.1; // 10%
  private readonly CACHE_HIT_RATE_THRESHOLD = 0.7; // 70%

  private constructor() {
    // Log stats every 10 minutes
    setInterval(() => this.logPeriodicStats(), 10 * 60 * 1000);
    
    // Clean up old history every hour
    setInterval(() => this.cleanupHistory(), 60 * 60 * 1000);
  }

  static getInstance(): LocalRedisMonitor {
    if (!LocalRedisMonitor.instance) {
      LocalRedisMonitor.instance = new LocalRedisMonitor();
    }
    return LocalRedisMonitor.instance;
  }

  /**
   * Record a Redis operation
   */
  recordOperation(operation: string, success: boolean = true): void {
    // Update operation counts
    const currentCount = this.operationCounts.get(operation) || 0;
    this.operationCounts.set(operation, currentCount + 1);

    // Add to history
    this.operationHistory.push({
      operation,
      timestamp: Date.now(),
      success
    });

    // Trim history if too large
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory = this.operationHistory.slice(-this.maxHistorySize);
    }

    // Check for alerts
    this.checkForAlerts();
  }

  /**
   * Record a cache hit/miss
   */
  recordCacheEvent(operation: string, hit: boolean): void {
    const eventType = hit ? `${operation}_cache_hit` : `${operation}_cache_miss`;
    this.recordOperation(eventType, true);
  }

  /**
   * Get current operation statistics
   */
  getStats(): {
    totalOperations: number;
    operationCounts: Record<string, number>;
    uptime: number;
    operationsPerHour: number;
    operationsPerDay: number;
    failureRate: number;
    cacheHitRate: number;
    recentAlerts: Array<{ message: string; timestamp: number; level: string }>;
  } {
    const now = Date.now();
    const uptimeMs = now - this.startTime;
    const uptimeHours = uptimeMs / (1000 * 60 * 60);
    const uptimeDays = uptimeMs / (1000 * 60 * 60 * 24);

    const totalOperations = Array.from(this.operationCounts.values()).reduce((sum, count) => sum + count, 0);
    
    // Calculate failure rate from recent history
    const recentOperations = this.operationHistory.filter(op => now - op.timestamp < 60 * 60 * 1000); // Last hour
    const failures = recentOperations.filter(op => !op.success).length;
    const failureRate = recentOperations.length > 0 ? failures / recentOperations.length : 0;

    // Calculate cache hit rate
    const cacheHits = this.operationCounts.get('cache_hit') || 0;
    const cacheMisses = this.operationCounts.get('cache_miss') || 0;
    const totalCacheOperations = cacheHits + cacheMisses;
    const cacheHitRate = totalCacheOperations > 0 ? cacheHits / totalCacheOperations : 0;

    return {
      totalOperations,
      operationCounts: Object.fromEntries(this.operationCounts),
      uptime: Math.round(uptimeMs / 1000), // in seconds
      operationsPerHour: uptimeHours > 0 ? Math.round(totalOperations / uptimeHours) : 0,
      operationsPerDay: uptimeDays > 0 ? Math.round(totalOperations / uptimeDays) : 0,
      failureRate: Math.round(failureRate * 100) / 100,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      recentAlerts: this.alerts.slice(-10) // Last 10 alerts
    };
  }

  /**
   * Check for various alert conditions
   */
  private checkForAlerts(): void {
    const stats = this.getStats();
    const now = Date.now();

    // Check hourly operation limit
    if (stats.operationsPerHour > this.HOURLY_OPERATION_LIMIT) {
      this.addAlert(
        `⚠️ High Redis usage: ${stats.operationsPerHour} operations/hour (limit: ${this.HOURLY_OPERATION_LIMIT})`,
        'warn'
      );
    }

    // Check daily operation limit
    if (stats.operationsPerDay > this.DAILY_OPERATION_LIMIT) {
      this.addAlert(
        `🚨 Very high Redis usage: ${stats.operationsPerDay} operations/day (limit: ${this.DAILY_OPERATION_LIMIT})`,
        'error'
      );
    }

    // Check failure rate
    if (stats.failureRate > this.HIGH_FAILURE_RATE_THRESHOLD) {
      this.addAlert(
        `❌ High failure rate: ${(stats.failureRate * 100).toFixed(1)}% (threshold: ${(this.HIGH_FAILURE_RATE_THRESHOLD * 100)}%)`,
        'warn'
      );
    }

    // Check cache hit rate
    if (stats.cacheHitRate < this.CACHE_HIT_RATE_THRESHOLD && stats.totalOperations > 100) {
      this.addAlert(
        `📉 Low cache hit rate: ${(stats.cacheHitRate * 100).toFixed(1)}% (target: ${(this.CACHE_HIT_RATE_THRESHOLD * 100)}%)`,
        'info'
      );
    }
  }

  /**
   * Add an alert
   */
  private addAlert(message: string, level: 'info' | 'warn' | 'error'): void {
    // Check if we already have this alert recently (avoid spam)
    const recentAlerts = this.alerts.filter(alert => Date.now() - alert.timestamp < 5 * 60 * 1000); // Last 5 minutes
    const isDuplicate = recentAlerts.some(alert => alert.message === message);

    if (!isDuplicate) {
      this.alerts.push({
        message,
        timestamp: Date.now(),
        level
      });

      // Trim alerts if too many
      if (this.alerts.length > this.maxAlerts) {
        this.alerts = this.alerts.slice(-this.maxAlerts);
      }

      // Log the alert
      switch (level) {
        case 'error':
          Logger.error(message);
          break;
        case 'warn':
          Logger.warn(message);
          break;
        case 'info':
          Logger.info(message);
          break;
      }
    }
  }

  /**
   * Log periodic statistics
   */
  private logPeriodicStats(): void {
    const stats = this.getStats();
    
    Logger.info('📊 Redis Usage Statistics:', {
      totalOperations: stats.totalOperations,
      operationsPerHour: stats.operationsPerHour,
      operationsPerDay: stats.operationsPerDay,
      failureRate: `${(stats.failureRate * 100).toFixed(1)}%`,
      cacheHitRate: `${(stats.cacheHitRate * 100).toFixed(1)}%`,
      uptime: `${Math.round(stats.uptime / 3600)}h`,
      topOperations: this.getTopOperations(5)
    });

    // Log recommendations if usage is high
    if (stats.operationsPerHour > this.HOURLY_OPERATION_LIMIT * 0.8) {
      Logger.warn('💡 Redis Usage Recommendations:', {
        recommendations: [
          'Increase cache TTL values',
          'Implement more aggressive batching',
          'Use in-memory caching for frequently accessed data',
          'Review and optimize query patterns',
          'Consider implementing read replicas for non-critical data'
        ]
      });
    }
  }

  /**
   * Get top operations by count
   */
  private getTopOperations(limit: number): Array<{ operation: string; count: number }> {
    return Array.from(this.operationCounts.entries())
      .map(([operation, count]) => ({ operation, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Clean up old history
   */
  private cleanupHistory(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const oldHistoryLength = this.operationHistory.length;
    
    this.operationHistory = this.operationHistory.filter(op => op.timestamp > oneHourAgo);
    
    const cleanedCount = oldHistoryLength - this.operationHistory.length;
    if (cleanedCount > 0) {
      Logger.info(`🧹 Cleaned up ${cleanedCount} old operation history entries`);
    }

    // Clean up old alerts
    const oldAlertsLength = this.alerts.length;
    this.alerts = this.alerts.filter(alert => Date.now() - alert.timestamp < 24 * 60 * 60 * 1000); // Keep 24 hours
    
    const cleanedAlerts = oldAlertsLength - this.alerts.length;
    if (cleanedAlerts > 0) {
      Logger.info(`🧹 Cleaned up ${cleanedAlerts} old alerts`);
    }
  }

  /**
   * Get detailed report for debugging
   */
  getDetailedReport(): {
    stats: ReturnType<LocalRedisMonitor['getStats']>;
    topOperations: Array<{ operation: string; count: number }>;
    recentFailures: Array<{ operation: string; timestamp: number }>;
    recommendations: string[];
  } {
    const stats = this.getStats();
    const recentFailures = this.operationHistory
      .filter(op => !op.success && Date.now() - op.timestamp < 60 * 60 * 1000)
      .slice(-10);

    const recommendations: string[] = [];
    
    if (stats.operationsPerHour > this.HOURLY_OPERATION_LIMIT * 0.5) {
      recommendations.push('Consider implementing more aggressive caching strategies');
    }
    
    if (stats.failureRate > 0.05) {
      recommendations.push('Investigate and fix Redis connection issues');
    }
    
    if (stats.cacheHitRate < 0.8) {
      recommendations.push('Optimize cache TTL values and cache warming strategies');
    }

    return {
      stats,
      topOperations: this.getTopOperations(10),
      recentFailures,
      recommendations
    };
  }

  /**
   * Reset all statistics (for testing)
   */
  reset(): void {
    this.operationCounts.clear();
    this.operationHistory.length = 0;
    this.alerts.length = 0;
    this.startTime = Date.now();
    Logger.info('🔄 Redis monitor statistics reset');
  }
}

export const localRedisMonitor = LocalRedisMonitor.getInstance();
