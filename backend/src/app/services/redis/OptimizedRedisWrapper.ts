import { Redis } from 'ioredis';
import { redisOperationLimiter } from './RedisOperationLimiter';
import { localRedisMonitor } from './LocalRedisMonitor';
import { Logger } from '../../config/logger';

/**
 * Optimized Redis Wrapper that dramatically reduces Redis operations
 * Uses aggressive caching and operation limiting to stay within free tier
 */
export class OptimizedRedisWrapper {
  private redis: Redis;
  private isConnected = false;

  constructor(redis: Redis) {
    this.redis = redis;
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      this.isConnected = true;
      Logger.info('✅ Optimized Redis wrapper connected');
    });

    this.redis.on('error', (error) => {
      this.isConnected = false;
      Logger.error('❌ Optimized Redis wrapper error:', error);
    });

    this.redis.on('close', () => {
      this.isConnected = false;
      Logger.warn('⚠️ Optimized Redis wrapper connection closed');
    });
  }

  /**
   * Get with aggressive caching (5 minute default TTL)
   */
  async get(key: string, cacheTtlSeconds = 300): Promise<string | null> {
    const result = await redisOperationLimiter.get(
      key,
      async () => {
        localRedisMonitor.recordOperation('get');
        return this.redis.get(key);
      },
      cacheTtlSeconds
    );

    // Record cache hit/miss
    localRedisMonitor.recordCacheEvent('get', result !== null);
    return result;
  }

  /**
   * Set with memory caching and batched Redis operations
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    const redisSetFn = ttlSeconds
      ? async () => {
          localRedisMonitor.recordOperation('setex');
          return this.redis.setex(key, ttlSeconds, value);
        }
      : async () => {
          localRedisMonitor.recordOperation('set');
          return this.redis.set(key, value);
        };

    return redisOperationLimiter.set(
      key,
      value,
      redisSetFn,
      ttlSeconds || 300
    );
  }

  /**
   * Set with expiration
   */
  async setex(key: string, seconds: number, value: string): Promise<boolean> {
    return this.set(key, value, seconds);
  }

  /**
   * Delete with memory cache cleanup
   */
  async del(key: string): Promise<number> {
    const success = await redisOperationLimiter.del(
      key,
      () => this.redis.del(key)
    );
    return success ? 1 : 0;
  }

  /**
   * Hash operations with caching
   */
  async hget(key: string, field: string): Promise<string | null> {
    const cacheKey = `${key}:${field}`;
    return redisOperationLimiter.get(
      cacheKey,
      () => this.redis.hget(key, field),
      300
    );
  }

  async hset(key: string, field: string, value: string): Promise<boolean> {
    const cacheKey = `${key}:${field}`;
    return redisOperationLimiter.set(
      cacheKey,
      value,
      () => this.redis.hset(key, field, value),
      300
    );
  }

  async hdel(key: string, field: string): Promise<number> {
    const cacheKey = `${key}:${field}`;
    const success = await redisOperationLimiter.del(
      cacheKey,
      () => this.redis.hdel(key, field)
    );
    return success ? 1 : 0;
  }

  /**
   * Get all hash fields (with aggressive caching)
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    const cached = await redisOperationLimiter.get(
      `${key}:*`,
      () => this.redis.hgetall(key),
      600 // 10 minute cache for hash objects
    );
    return cached || {};
  }

  /**
   * Set operations with caching
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    // For sets, we'll use a simplified approach - cache the entire set
    try {
      const result = await this.redis.sadd(key, ...members);
      // Invalidate cache for this set
      redisOperationLimiter.del(`set:${key}`, () => Promise.resolve());
      return result;
    } catch (error) {
      Logger.error(`Redis SADD failed for key ${key}:`, error);
      return 0;
    }
  }

  async smembers(key: string): Promise<string[]> {
    const cached = await redisOperationLimiter.get(
      `set:${key}`,
      () => this.redis.smembers(key),
      300
    );
    return cached || [];
  }

  /**
   * Increment operations (critical for counters)
   */
  async incr(key: string): Promise<number> {
    try {
      const result = await this.redis.incr(key);
      // Cache the new value briefly
      redisOperationLimiter.set(key, result.toString(), () => Promise.resolve(), 60);
      return result;
    } catch (error) {
      Logger.error(`Redis INCR failed for key ${key}:`, error);
      return 1; // Return 1 as fallback
    }
  }

  async decr(key: string): Promise<number> {
    try {
      const result = await this.redis.decr(key);
      // Cache the new value briefly
      redisOperationLimiter.set(key, result.toString(), () => Promise.resolve(), 60);
      return result;
    } catch (error) {
      Logger.error(`Redis DECR failed for key ${key}:`, error);
      return 0; // Return 0 as fallback
    }
  }

  /**
   * Expiration operations
   */
  async expire(key: string, seconds: number): Promise<number> {
    try {
      return await this.redis.expire(key, seconds);
    } catch (error) {
      Logger.error(`Redis EXPIRE failed for key ${key}:`, error);
      return 0;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      Logger.error(`Redis TTL failed for key ${key}:`, error);
      return -1;
    }
  }

  /**
   * Existence check with caching
   */
  async exists(key: string): Promise<number> {
    const cached = await redisOperationLimiter.get(
      `exists:${key}`,
      async () => {
        const result = await this.redis.exists(key);
        return result.toString();
      },
      60 // Short cache for existence checks
    );
    return cached ? parseInt(cached) : 0;
  }

  /**
   * Ping operation (for health checks)
   */
  async ping(): Promise<string> {
    try {
      return await this.redis.ping();
    } catch (error) {
      Logger.error('Redis PING failed:', error);
      throw error;
    }
  }

  /**
   * Keys operation (avoid in production, but needed for cleanup)
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      Logger.warn(`⚠️ Redis KEYS operation called with pattern: ${pattern} - this is expensive!`);
      return await this.redis.keys(pattern);
    } catch (error) {
      Logger.error(`Redis KEYS failed for pattern ${pattern}:`, error);
      return [];
    }
  }

  /**
   * Pipeline operations (for batch processing)
   */
  pipeline() {
    return this.redis.pipeline();
  }

  /**
   * Get connection status
   */
  get status(): string {
    return this.redis.status;
  }

  /**
   * Get operation stats
   */
  getOperationStats() {
    return {
      limiter: redisOperationLimiter.getStats(),
      monitor: localRedisMonitor.getStats()
    };
  }

  /**
   * Get detailed monitoring report
   */
  getDetailedReport() {
    return localRedisMonitor.getDetailedReport();
  }

  /**
   * Disconnect
   */
  async disconnect(): Promise<void> {
    try {
      await this.redis.disconnect();
    } catch (error) {
      Logger.error('Error disconnecting optimized Redis wrapper:', error);
    }
  }

  /**
   * Get the underlying Redis client (use sparingly)
   */
  getClient(): Redis {
    Logger.warn('⚠️ Direct Redis client access - operations will not be limited!');
    return this.redis;
  }
}

export default OptimizedRedisWrapper;
